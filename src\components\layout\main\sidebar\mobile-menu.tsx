"use client";

import { cn } from "@/lib/utils";
import { routeList } from "@/lib/route-list";
import { Button } from "@/components/ui/button";
import {
	Tooltip,
	TooltipTrigger,
	TooltipContent,
	TooltipProvider,
} from "@/components/ui/tooltip";
import { Link, useNavigate } from "@tanstack/react-router";
import { SheetClose } from "@/components/ui/sheet";
import { useAuthStore } from "@/features/auth/store";
import { useShallow } from "zustand/react/shallow";

interface MenuProps {
	isOpen: boolean | undefined;
	userRole?: string;
}

export function MobileMenu({ isOpen, userRole }: MenuProps) {
	const navigate = useNavigate();
	const { logout } = useAuthStore(
		useShallow((state) => ({
			logout: state.logout,
		}))
	);
	const pathname = window.location.pathname;
	const menuList = routeList.filter(
		(menu) => !(menu.exclude || menu.desktopOnly)
	);

	const handleClick = async (e: React.MouseEvent<HTMLButtonElement>) => {
		const value = e.currentTarget.value;
		switch (value) {
			case "Logout":
				await logout();
				navigate({ to: "/account/login" });
				break;
			default:
				break;
		}
	};

	const isComingSoon = (label: string) => {
		return (
			label !== "Subject Test" &&
			label !== "Resources" &&
			label !== "Custom Test" &&
			label !== "Mock Test" &&
			label !== "AI Test" &&
			label !== "Dashboard" &&
			label !== "Analytics" &&
			label !== "Logout"
		);
	};

	const isDisabledForUser = (disabledForFree?: boolean) => {
		return disabledForFree && userRole === "free";
	};

	return (
		<ul className="flex border-y border-y-[#E2E8F01A] grow flex-col items-start space-y-1 px-2 py-6">
			{menuList.map(({ groupLabel, menus }, groupIndex) => (
				<li
					key={groupIndex}
					className={cn(
						"flex w-full flex-col",
						groupLabel ? "pt-5" : groupIndex !== 0 && "border-t"
					)}
				>
					{groupLabel && (
						<p className="text-sm font-bold text-slate-300 px-4 pb-2 max-w-[248px] truncate uppercase dark:text-gray-300">
							{groupLabel}
						</p>
					)}
					{menus.map(
						({ href, label, icon: Icon, active, disabledForFree }, index) => (
							<div className="w-full relative" key={index}>
								<TooltipProvider disableHoverableContent>
									<Tooltip delayDuration={100}>
										<TooltipTrigger asChild>
											<SheetClose asChild>
												<Button
													variant={
														(active === undefined &&
															href.length > 0 &&
															pathname.startsWith(href) &&
															!isComingSoon(label)) ||
														active
															? "active"
															: "inactive"
													}
													className={cn(
														"rounded-full text-base w-full justify-start gap-3 h-11 bg-primary text-primary-foreground hover:brightness-90 pr-20",
														isComingSoon(label) &&
															"opacity-70 cursor-not-allowed"
													)}
													value={label}
													onClick={
														isComingSoon(label) ? undefined : handleClick
													}
													asChild={href.length > 0 && !isComingSoon(label)}
												>
													{href.length > 0 && !isComingSoon(label) ? (
														<Link to={href}>
															<span
																className={cn(isOpen === false ? "" : "mr-0")}
															>
																<Icon size={24} />
															</span>
															<p
																className={cn(
																	"max-w-[200px] truncate",
																	isOpen === false
																		? "-translate-x-96 opacity-0"
																		: "translate-x-0 opacity-100"
																)}
															>
																{label}
															</p>
														</Link>
													) : (
														<>
															<span
																className={cn(isOpen === false ? "" : "mr-0")}
															>
																<Icon size={24} />
															</span>
															<p
																className={cn(
																	"max-w-[200px] truncate",
																	isOpen === false
																		? "-translate-x-96 opacity-0"
																		: "translate-x-0 opacity-100"
																)}
															>
																{label}
															</p>
														</>
													)}
												</Button>
											</SheetClose>
										</TooltipTrigger>
										{isOpen === false && (
											<TooltipContent side="right">{label}</TooltipContent>
										)}
									</Tooltip>
								</TooltipProvider>

								{isComingSoon(label) && (
									<div className="absolute right-2 top-1/2 -translate-y-1/2 text-xs py-0.5 px-1 border border-accent text-accent rounded-md whitespace-nowrap">
										Coming Soon
									</div>
								)}
								{!isComingSoon(label) && isDisabledForUser(disabledForFree) && (
									<div className="absolute right-2 top-1/2 -translate-y-1/2 text-xs py-0.5 px-1 border border-orange-500 text-orange-500 rounded-md whitespace-nowrap">
										Pro Only
									</div>
								)}
							</div>
						)
					)}
				</li>
			))}
		</ul>
	);
}
