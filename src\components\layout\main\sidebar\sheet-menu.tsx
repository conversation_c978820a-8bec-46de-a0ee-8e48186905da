import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { ICONS } from "@/lib/assets/images";
import { Menu as MenuIcon } from "react-feather";
import { UserNav } from "./user-nav";
import { MobileMenu } from "./mobile-menu";
import { UpgradePlan } from "../upgrade-plan";
import { Link } from "@tanstack/react-router";
import { useGetUser } from "@/lib/queries/user.query";
import { useAuthStore } from "@/features/auth/store";

export function SheetMenu() {
	const { user } = useAuthStore((state) => ({ user: state.user }));
	const { data: userData } = useGetUser(user?.uid || "");
	const userRole = userData?.data?.data?.role;

	return (
		<Sheet>
			<SheetTrigger className="lg:hidden" asChild>
				<MenuIcon size={24} />
			</SheetTrigger>
			<SheetContent
				className=" gap-y-3 w-4/5 pt-8 rounded-r-xl bg-primary px-3 h-full flex flex-col justify-between"
				side="left"
			>
				<SheetTitle className="hidden">Menu</SheetTitle>
				<SheetHeader className="text-left">
					{/* <Button
            className="flex items-start justify-start pb-2 pt-1"
            variant="link"
            asChild
          > */}
					<div className="pb-2">
						<img src={ICONS.expandedwhite} alt="Logo" className="h-10 w-1/2" />
					</div>
					{/* </Button> */}
				</SheetHeader>
				<UserNav />
				<MobileMenu isOpen userRole={userRole} />
				<Link to="/selectplan">
					<UpgradePlan />
				</Link>
			</SheetContent>
		</Sheet>
	);
}
