import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
	Toolt<PERSON>,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";

type BottomActionsBarProps = {
	mcqs: { value: number; cap: number; label?: string };
	count: { value: number; label?: string };

	onReset: () => void;
	onAttempt: () => void;

	loading?: boolean;
	attemptDisabled?: boolean;
	disabledTooltip?: string;

	className?: string;
};

const BottomActionsBar: React.FC<BottomActionsBarProps> = ({
	mcqs,
	count,
	onReset,
	onAttempt,
	loading = false,
	attemptDisabled = false,
	disabledTooltip,
	className = "",
}) => {
	const AttemptButton = (
		<Button
			className="rounded-xl px-5 py-3 bg-[#5936cd] text-white hover:bg-[#4c2dae] disabled:opacity-60"
			disabled={loading || attemptDisabled}
			onClick={onAttempt}
		>
			{loading ? (
				<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
			) : (
				<svg
					className="w-4 h-4"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
				>
					<path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" />
					<path d="M20 22H6.5a2.5 2.5 0 0 1 0-5H20z" />
					<path d="M8 6h13M8 12h13" />
				</svg>
			)}
			<span className="ml-2">Attempt</span>
		</Button>
	);

	const MobileAttemptButton = (
		<Button
			className="rounded-xl flex items-center justify-center h-full px-3 py-2 bg-[#5936cd] text-white hover:bg-[#4c2dae] disabled:bg-[#5936cd] disabled:opacity-60 focus-visible:ring-2 focus-visible:ring-[#5936cd]/50 text-sm w-full"
			disabled={loading || attemptDisabled}
			onClick={onAttempt}
		>
			{loading ? (
				<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
			) : (
				<svg
					className="w-4 h-4 flex-shrink-0"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					strokeWidth="2"
					strokeLinecap="round"
					strokeLinejoin="round"
				>
					<path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" />
					<path d="M20 22H6.5a2.5 2.5 0 0 1 0-5H20z" />
					<path d="M8 6h13M8 12h13" />
				</svg>
			)}
			<span className="ml-2">Attempt</span>
		</Button>
	);

	return (
		<>
			{/* Desktop / Tablet */}
			<div
				className={`hidden lg:inline-flex items-center rounded-2xl border border-gray-200 bg-white px-3 py-2 shadow-sm w-fit ${className}`}
			>
				<div className="flex items-center gap-3">
					<div className="flex items-center gap-2">
						<div className="text-sm text-gray-700">
							<div className="text-xs text-gray-500">
								{mcqs.label ?? "Total MCQs"}
							</div>
							<div className="font-medium">
								{mcqs.value} / {mcqs.cap}
							</div>
						</div>
					</div>

					<span className="h-8 w-px bg-gray-200" />

					{/* topics/chapters count */}
					<div className="flex items-baseline gap-1 text-gray-700 px-2 py-1 rounded-md">
						<span className="font-semibold">{count.value}</span>
						<span className="text-sm">{count.label ?? "items"}</span>
					</div>
				</div>

				<div className="mx-5 h-8 w-px bg-gray-200" />

				{/* ACTIONS */}
				<div className="flex items-center gap-3">
					<Button
						variant="outline"
						size="sm"
						className="rounded-xl px-4 border border-gray-200 bg-gray-50 text-gray-700 hover:bg-gray-100 gap-2"
						onClick={onReset}
					>
						<svg
							className="w-4 h-4"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
						>
							<path d="M21 12a9 9 0 1 1-3.51-7.06" />
							<polyline points="21 3 21 9 15 9" />
						</svg>
						Reset
					</Button>

					{disabledTooltip && attemptDisabled ? (
						<TooltipProvider>
							<Tooltip>
								<TooltipTrigger asChild>
									<span className="inline-block">{AttemptButton}</span>
								</TooltipTrigger>
								<TooltipContent side="top">
									<p>{disabledTooltip}</p>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>
					) : (
						AttemptButton
					)}
				</div>
			</div>

			{/* Mobile sticky bar  */}
			<div
				className="lg:hidden fixed inset-x-0 z-50 px-4"
				style={{ bottom: "max(1.25rem, env(safe-area-inset-bottom))" }}
			>
				<div className="mx-auto max-w-sm h-16 p-2 grid grid-cols-2 gap-2 rounded-2xl shadow-xl bg-white">
					<Button
						variant="outline"
						className="rounded-xl flex items-center justify-center h-full px-3 py-2 border-gray-300 hover:bg-gray-50 text-sm w-full"
						onClick={onReset}
					>
						<svg
							className="w-4 h-4 flex-shrink-0"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
							strokeLinecap="round"
							strokeLinejoin="round"
						>
							<path d="M21 12a9 9 0 1 1-3.51-7.06" />
							<polyline points="21 3 21 9 15 9" />
						</svg>
						<span className="ml-2">Reset</span>
					</Button>

					{disabledTooltip && attemptDisabled ? (
						<TooltipProvider>
							<Tooltip>
								<TooltipTrigger asChild>
									<span className="flex w-full h-full">
										{MobileAttemptButton}
									</span>
								</TooltipTrigger>
								<TooltipContent side="top">
									<p>{disabledTooltip}</p>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>
					) : (
						MobileAttemptButton
					)}
				</div>
			</div>

			<div className="lg:hidden h-20" aria-hidden="true" />
		</>
	);
};

export default BottomActionsBar;
