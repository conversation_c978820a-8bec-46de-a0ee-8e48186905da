import { useQuery } from "@tanstack/react-query";
import {
	getMockTests,
	getAllQuizzes,
	getQuiz,
} from "@/features/tests/services";
import { getBookmarksList } from "@/features/bookmarks/services";
import { DataMap, getBookmarksReq } from "@/features/bookmarks/types";

export const useGetMockTests = () => {
	return useQuery({
		queryKey: ["mockTests"],
		queryFn: getMockTests,
		staleTime: Infinity,
		refetchOnWindowFocus: false,
		select: (data) => data.data.data,
	});
};

export const useGetAllQuizzes = (page: number = 1, limit: number = 10) => {
	return useQuery({
		queryKey: ["allQuizzes", page, limit],
		queryFn: () => getAllQuizzes(page, limit),
		refetchOnWindowFocus: false,
		select: (data) => data.data.data,
	});
};

export const useGetBookmarks = <T extends keyof DataMap>(
	params: getBookmarksReq<T>
) => {
	return useQuery({
		queryKey: ["bookmarks", params.category, params.page, params.limit],
		queryFn: () => getBookmarksList(params),
		refetchOnWindowFocus: false,
		select: (data) => data.data.data,
	});
};

export const useGetQuiz = (quizId: string) => {
	return useQuery({
		queryKey: ["quiz", quizId],
		queryFn: () => getQuiz(quizId),
		enabled: !!quizId,
		refetchOnWindowFocus: false,
		select: (data) => data.data.data,
	});
};
