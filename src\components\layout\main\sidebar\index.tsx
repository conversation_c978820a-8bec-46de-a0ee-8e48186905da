"use client";
import { useSidebar } from "@/hooks/use-sidebar";
import { useStore } from "@/hooks/use-store";
import { SidebarToggle } from "./sidebar-toggle";
import { Menu } from "./menu";
import { cn } from "@/lib/utils";
import { useGetUser } from "@/lib/queries/user.query";
import { useAuthStore } from "@/features/auth/store";
import { useEffect } from "react";
import { useShallow } from "zustand/react/shallow";
import { useMockTestStore } from "@/features/tests/mock-test.store";

export default function Sidebar() {
	const sidebar = useStore(useSidebar, (x) => x);
	const { user } = useAuthStore((state) => ({ user: state.user }));
	const { data: userData } = useGetUser(user?.uid || "");
	const userRole = userData?.data?.data?.role;
	const userQuizzesAttempted = userData?.data?.data?.quizzes_attempted;

	// Initialize mock test attempts from API data
	const { setAttemptedMockTests } = useMockTestStore(
		useShallow((state) => ({
			setAttemptedMockTests: state.setAttemptedMockTests,
		}))
	);
	console.log("userQuizzesAttempted", sidebar);

	useEffect(() => {
		if (userQuizzesAttempted !== undefined) {
			setAttemptedMockTests(userQuizzesAttempted);
		}
	}, [userQuizzesAttempted, setAttemptedMockTests]);

	if (!sidebar) return null;
	const { isOpen, toggleOpen, getOpenState, setIsHover, settings } = sidebar;
	return (
		<aside
			className={cn(
				"fixed top-0 left-0 z-10 shadow bg-white -translate-x-full lg:translate-x-0 transition-[width] ease-in-out duration-300",
				!getOpenState() ? "w-[90px]" : "w-[280px]",
				settings.disabled && "hidden"
			)}
		>
			<SidebarToggle isOpen={isOpen} setIsOpen={toggleOpen} />
			<div
				onMouseEnter={() => setIsHover(true)}
				onMouseLeave={() => setIsHover(false)}
				className="relative h-full flex flex-col px-3 py-4 overflow-y-auto shadow-md dark:shadow-zinc-800"
			>
				<Menu isOpen={getOpenState()} userRole={userRole} />
			</div>
		</aside>
	);
}
