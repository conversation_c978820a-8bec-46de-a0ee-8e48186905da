import { useMediaQuery } from "react-responsive";
import { Navbar } from "@/components/layout/main/navbar";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect, useMemo, useState } from "react";
import { useToast } from "@/hooks/use-toast";
import {
	generateCustomTest,
	CustomTestSelection,
	getCustomTestParameters,
} from "@/features/tests/custom.api";
import { Subject, SelectedTopic, TopicFormData } from "@/features/tests/custom";
import BottomActionsBar from "@/components/tests/BottomActionsBar";
import SubjectCards from "@/components/tests/SubjectCards";
import TestSelectionModal from "@/components/tests/TestSelectionModel";
import PrepBanner from "@/components/common/PrepBanner";
import { useGetMockTests } from "@/lib/queries/tests.query";
import { useGetUser } from "@/lib/queries/user.query";
import { useAuthStore } from "@/features/auth/store";
import { useShallow } from "zustand/react/shallow";
const slug = (s: string) =>
	s
		.toLowerCase()
		.replace(/[^a-z0-9]+/g, "-")
		.replace(/(^-|-$)/g, "");

function CustomTestPage() {
	const { toast } = useToast();
	const navigate = useNavigate();

	// Check user role
	const { user } = useAuthStore(
		useShallow((state) => ({
			user: state.user,
		}))
	);
	const { data: userData } = useGetUser(user?.uid || "");
	const userRole = userData?.data?.data?.role;

	const [showTestModal, setShowTestModal] = useState(false);
	const [selectedPrep, setSelectedPrep] = useState("NUST-Engineering");

	const [loading, setLoading] = useState(false);

	const [subjects, setSubjects] = useState<Subject[]>([]);
	const [selectedTopics, setSelectedTopics] = useState<SelectedTopic[]>([]);
	const [topicFormData, setTopicFormData] = useState<
		Record<string, TopicFormData>
	>({});
	const [comboMaps, setComboMaps] = useState<
		Record<string, Record<string, number>>
	>({});
	const [loadingParams, setLoadingParams] = useState(false);
	const [paramsError, setParamsError] = useState<string | null>(null);

	// Fetch mock tests using TanStack Query
	const {
		data: mockTestsData,
		isLoading: loadingTests,
		error: testsErrorObj,
	} = useGetMockTests();
	const tests = mockTestsData?.mockTests ?? [];
	const testsError = testsErrorObj
		? (testsErrorObj as any)?.response?.data?.message || "Failed to load tests."
		: null;

	// Set initial selected prep when tests are loaded
	useEffect(() => {
		if (tests.length > 0 && !tests.some((t) => t.testId === selectedPrep)) {
			const first = tests[0];
			if (first) {
				setSelectedPrep(first.testId);
			}
		}
	}, [tests, selectedPrep]);

	const selectedTestName = useMemo(
		() => tests.find((t) => t.testId === selectedPrep)?.name || selectedPrep,
		[tests, selectedPrep]
	);

	useEffect(() => {
		let cancelled = false;

		async function load() {
			try {
				setLoadingParams(true);
				setParamsError(null);

				const res = await getCustomTestParameters(selectedPrep);
				const data = res.data?.data;

				const newSubjects: Subject[] = [];
				const newComboMaps: Record<string, Record<string, number>> = {};

				Object.entries(data.subjects).forEach(([subjectName, s]: any) => {
					const id = slug(subjectName);

					const tset = new Set<string>();
					const cm: Record<string, number> = {};
					(s.combinations ?? []).forEach((c: any) => {
						(c.availableCombinations ?? []).forEach((ac: any) => {
							const key =
								`${c.topic}__${ac.type}__${ac.difficulty}`.toLowerCase();
							if ((ac.count ?? 0) > 0) {
								cm[key] = ac.count ?? 0;
								tset.add(c.topic);
							}
						});
					});

					newSubjects.push({
						id,
						name: subjectName,
						count: 0,
						topics: Array.from(tset).sort(),
					});

					newComboMaps[id] = cm;
				});

				setSubjects(newSubjects);
				setComboMaps(newComboMaps);

				setSelectedTopics([]);
				setTopicFormData({});
			} catch (e: any) {
				setParamsError(
					e?.response?.data?.message || "Failed to load parameters."
				);
				setSubjects([]);
				setComboMaps({});
			} finally {
				if (!cancelled) setLoadingParams(false);
			}
		}

		if (selectedPrep) load();
		return () => {
			cancelled = true;
		};
	}, [selectedPrep]);

	const totalSelectedMCQs = useMemo(() => {
		return selectedTopics.reduce(
			(acc, t) => acc + (parseInt(t.numQuestions, 10) || 0),
			0
		);
	}, [selectedTopics]);

	const handleAttemptCustomTest = async () => {
		if (selectedTopics.length === 0) {
			toast({
				title: "Error",
				description:
					"Please select at least one topic before attempting a test.",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);

			const mappedSelections: CustomTestSelection[] = selectedTopics.map(
				(topic) => {
					const subject = subjects.find((s) => s.id === topic.subjectId);
					return {
						subject: subject?.name || topic.subjectId,
						topic: topic.name,
						type: topic.testType.toLowerCase(),
						difficulty: topic.difficulty.toLowerCase(),
						count: parseInt(topic.numQuestions, 10) || 1,
					};
				}
			);

			const response = await generateCustomTest({
				entryTest: selectedPrep, // testId
				customSelections: mappedSelections,
			});

			if (response.data && response.data.success) {
				localStorage.setItem(
					"currentTestData",
					JSON.stringify(response.data.data)
				);
				navigate({ to: "/mcqs" });
				toast({
					title: "Success",
					description: "Custom test generated successfully!",
				});
			} else {
				throw new Error("Invalid response format");
			}
		} catch (error: any) {
			toast({
				title: "Error",
				description:
					error?.response?.data?.message ||
					"Failed to generate custom test. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	const handlePrepChange = (prepTestId: string) => {
		setSelectedPrep(prepTestId);
		setShowTestModal(false);
	};

	const resetTest = () => {
		setSelectedTopics([]);
		setTopicFormData({});
		setSubjects((prev) => prev.map((s) => ({ ...s, count: 0 })));
		toast({ title: "Reset", description: "Data reset successfully!" });
	};

	const filteredSubjects = subjects;
	const totalSelectedTopics = selectedTopics.length;

	const mcqCap = 200;
	const capped = totalSelectedMCQs >= mcqCap;

	const isDesktop = useMediaQuery({ minWidth: 1024 });

	return (
		<div className='min-h-screen bg-gray-50 font-["Inter",sans-serif]'>
			{!isDesktop && <Navbar />}
			<div className="max-w-[1160px] mx-auto p-6">
				{/* Header */}
				<div className="mb-8">
					<div className="sm:hidden flex items-center justify-between mb-4">
						<div className="w-9" />
					</div>
					<PrepBanner
						className="mb-10"
						value={selectedTestName}
						onChangeClick={() => setShowTestModal(true)}
						maxPillWidth={360}
					/>

					<div className="flex flex-col items-start gap-2 sm:gap-[15px] w-full">
						<h1 className="font-inter font-bold text-2xl sm:text-[32px] leading-[30px] sm:leading-[39px] text-[#211C37]">
							Custom Test
						</h1>

						<div className="w-full sm:hidden flex items-start justify-between gap-3">
							<p className="font-inter font-normal text-sm leading-[18px] text-[#64748B] pr-4">
								Your test, your rules. Personalized to fit your study needs.
							</p>

							<div className="text-right space-y-1 shrink-0">
								<div className="text-xs text-gray-500">
									{totalSelectedTopics} topic(s)
								</div>
								<div className="text-sm text-gray-700">
									<div className="text-xs text-gray-500">Total MCQs</div>
									<div
										className={`font-medium ${capped ? "text-red-600" : ""}`}
									>
										{totalSelectedMCQs} / {mcqCap}
									</div>
								</div>
							</div>
						</div>

						<p className="font-inter font-normal text-sm sm:text-base leading-[18px] sm:leading-[19px] text-[#64748B] pr-4 hidden sm:block">
							Your test, your rules. Personalized to fit your study needs.
						</p>
					</div>
				</div>

				{/* Parameters Loading/Error */}
				{loadingParams && (
					<div className="mb-6 text-sm text-gray-600">Loading subjects…</div>
				)}
				{paramsError && (
					<div className="mb-6 text-sm text-red-600">{paramsError}</div>
				)}

				{/* Subject Cards */}
				<SubjectCards
					mode="custom"
					subjects={filteredSubjects}
					selectedTopics={selectedTopics}
					setSelectedTopics={setSelectedTopics}
					comboMaps={comboMaps}
					setComboMaps={setComboMaps}
					topicFormData={topicFormData}
					setTopicFormData={setTopicFormData}
					totalSelectedMCQs={totalSelectedMCQs}
				/>

				{/* Bottom Actions */}
				<div className="mt-8 flex justify-center">
					<BottomActionsBar
						mcqs={{ value: totalSelectedMCQs, cap: mcqCap }}
						count={{ value: totalSelectedTopics, label: "chapters" }}
						onReset={resetTest}
						onAttempt={handleAttemptCustomTest}
						loading={loading}
						attemptDisabled={
							selectedTopics.length === 0 || loading || userRole === "free"
						}
						disabledTooltip={
							userRole === "free"
								? "Upgrade to Pro for this feature."
								: undefined
						}
					/>
				</div>

				{/* Test Selection Modal */}
				<TestSelectionModal
					open={showTestModal}
					tests={tests}
					selectedPrep={selectedPrep}
					loading={loadingTests}
					error={testsError}
					onClose={() => setShowTestModal(false)}
					onSelect={handlePrepChange}
				/>
			</div>
		</div>
	);
}

export const Route = createFileRoute("/(app)/_mainlayout/t/custom")({
	component: CustomTestPage,
});
