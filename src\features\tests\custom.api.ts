import { api } from "@/lib/api";
import { APIResponse } from "@/lib/api/types";
import { AxiosResponse } from "axios";

// Types for Custom Test API
export interface SubjectParameters {
  totalMcqs: number; // subject-wide available MCQs
  availableTypes: string[];
  availableDifficulties: string[];
  topics: Array<{
    topic: string;
    totalMcqs: number; // available MCQs in this topic (any type/difficulty)
  }>;
  combinations: Array<{
    topic: string;
    totalTopicMcqs: number;
    availableCombinations: Array<{
      type: string;
      difficulty: string;
      count: number; // available MCQs for (topic, type, difficulty)
    }>;
  }>;
}

export interface CustomTestParameters {
  entryTest: string;
  entryTestConfig: {
    university: string;
    name: string;
    description: string;
    defaultWeightage: Record<string, number>;
    defaultTime: number;
  };
  subjects: Record<string, SubjectParameters>;
  availableSubjects: string[];
}

export interface CustomTestSelection {
  subject: string;
  topic: string;
  type: string;
  difficulty: string;
  count: number;
}

export interface GenerateCustomTestRequest {
  entryTest: string;
  customSelections: CustomTestSelection[];
}

export interface CustomTestMCQ {
  _id: string;
  subject: string;
  type: string;
  title: string;
  topic: string;
  difficulty: "easy" | "medium" | "hard";
  repitition: boolean;
  resource?: string;
  explanation: string;
  entryTest: string[];
  options: string[];
  answer: number;
  chapter: number;
}

export interface CustomTestResponse {
  quizId: string;
  mcqsBySubject: {
    [subject: string]: CustomTestMCQ[];
  };
  count: number;
  subjectCounts: {
    [subject: string]: number;
  };
  time: {
    minutes: number;
    seconds: number;
  };
  requestedParams: {
    entryTest: string;
    customSelections: CustomTestSelection[];
  };
}

export interface MockTest {
  testId: string;
  university: string;
  name: string;
  description: string;
}

export interface GetMockTestsResponse {
  mockTests: MockTest[];
  count: number;
}

export type SubjectTopicSelection = { topic: string; count: number };

export type SubjectSelection = {
  subject: string;
  topics: SubjectTopicSelection[];
};

export interface GenerateSubjectTestRequest {
  entryTest: string; 
  subjectSelections: SubjectSelection[];
}

export interface GenerateSubjectTestResponseData {
  quizId: string | null;
  mcqsBySubject: Record<string, any[]>;
  count: number;
  subjectCounts: Record<string, number>;
  time: { minutes: number; seconds: number };
  requestedParams: {
    entryTest: string;
    subjectSelections: SubjectSelection[];
  };
  quizStructure: Array<{
    subject: string;
    topic: string;
    requestedCount: number;
    actualCount: number;
  }>;
  mcqStats: {
    newMcqs: number;
    repeatedMcqs: number;
    totalPreviouslySeen: number;
  };
}


export const generateSubjectTest = (payload: GenerateSubjectTestRequest) =>
  api.post<APIResponse<GenerateSubjectTestResponseData>>(
    "/quiz/subject/generate",
    payload
  );

export const getCustomTestParameters = async (
  entryTest: string
): Promise<AxiosResponse<APIResponse<CustomTestParameters>>> => {
  return await api.get<APIResponse<CustomTestParameters>>(
    `/quiz/custom/parameters?entryTest=${entryTest}`
  );
};

export const getSubjectTestParameters = (entryTest: string) =>
  api.get(`/quiz/subject/parameters`, { params: { entryTest } });

export const generateCustomTest = async (
  request: GenerateCustomTestRequest
): Promise<AxiosResponse<APIResponse<CustomTestResponse>>> => {
  return await api.post<APIResponse<CustomTestResponse>>(
    "/quiz/custom/generate",
    request
  );
};