import { create } from "zustand";

interface MockTestStore {
	attemptedMockTests: number;
	setAttemptedMockTests: (count: number) => void;
	incrementAttemptedMockTests: () => void;
}

export const useMockTestStore = create<MockTestStore>()((set) => ({
	attemptedMockTests: 0,
	setAttemptedMockTests: (count: number) => set({ attemptedMockTests: count }),
	incrementAttemptedMockTests: () =>
		set((state) => ({ attemptedMockTests: state.attemptedMockTests + 1 })),
}));

